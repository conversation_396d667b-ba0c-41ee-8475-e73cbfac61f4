package com.firstedu.marsladder.falcon.classroom.controller

import com.fasterxml.jackson.databind.ObjectMapper
import com.firstedu.marsladder.falcon.classroom.ClassroomFileState
import com.firstedu.marsladder.falcon.classroom.ClassroomFileType
import com.firstedu.marsladder.falcon.classroom.controller.dto.CreateClassroomFileRequest
import com.firstedu.marsladder.falcon.classroom.controller.dto.UpdateClassroomFileNameRequest
import com.firstedu.marsladder.falcon.classroom.controller.dto.UpdateClassroomFileStateRequest
import com.firstedu.marsladder.falcon.classroom.service.ClassroomFileService
import com.firstedu.marsladder.falcon.classroom.service.ClassroomService
import com.firstedu.marsladder.falcon.classroom.service.domain.ClassroomFile
import com.firstedu.marsladder.falcon.classroom.service.domain.ClassroomFolderStructure
import com.firstedu.marsladder.falcon.config.S3ClassroomFileProperties
import com.firstedu.marsladder.falcon.config.S3Properties
import com.firstedu.marsladder.falcon.file.service.domain.S3File
import com.firstedu.marsladder.falcon.security.FalconJwtAuthenticationTokenConverter
import com.firstedu.marsladder.falcon.security.SessionProvider
import com.firstedu.marsladder.falcon.security.WebSecurityConfig
import com.firstedu.marsladder.falcon.user.WebMvcTestConfiguration
import com.firstedu.marsladder.falcon.utils.XssFilter
import com.nhaarman.mockitokotlin2.any
import com.nhaarman.mockitokotlin2.doNothing
import com.nhaarman.mockitokotlin2.times
import com.nhaarman.mockitokotlin2.verify
import com.nhaarman.mockitokotlin2.whenever
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.boot.test.mock.mockito.MockBean
import org.springframework.context.annotation.ComponentScan
import org.springframework.context.annotation.FilterType
import org.springframework.context.annotation.Import
import org.springframework.http.MediaType
import org.springframework.mock.web.MockMultipartFile
import org.springframework.security.test.context.support.WithMockUser
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.delete
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.patch
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status
import java.time.LocalDateTime

@WebMvcTest(
    controllers = [ClassroomFileController::class],
    excludeFilters = [
        ComponentScan.Filter(
            type = FilterType.ASSIGNABLE_TYPE,
            classes = [
                FalconJwtAuthenticationTokenConverter::class,
            ],
        ),
    ],
)
@Import(WebSecurityConfig::class, WebMvcTestConfiguration::class)
internal class ClassroomFileControllerTest
    @Autowired
    private constructor(
        private val mockMvc: MockMvc,
        private val objectMapper: ObjectMapper,
    ) {
        @MockBean
        private lateinit var classroomService: ClassroomService

        @MockBean
        private lateinit var sessionProvider: SessionProvider

        @MockBean
        private lateinit var classroomFileService: ClassroomFileService

        @MockBean
        private lateinit var xssFilter: XssFilter

        @MockBean
        private lateinit var s3Properties: S3Properties

        @BeforeEach
        internal fun setUp() {
            whenever(sessionProvider.getUserId()).thenReturn(mockUserId)
            whenever(classroomService.hasPermissionToReadOrUpdateClassroom(classroomId, mockUserId)).thenReturn(true)
            whenever(xssFilter.filter(any())).thenReturn("clean-name")
            whenever(s3Properties.classroomFile).thenReturn(
                S3ClassroomFileProperties(
                    "test-marsladder-classroom-files",
                    50 * 1024 * 1024L,
                    mapOf(
                        "PNG" to "image/jpeg",
                        "JPG" to "image/jpeg",
                        "JPEG" to "image/jpeg",
                        "PDF" to "application/pdf",
                        "DOC" to "application/msword",
                        "DOCX" to "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                        "PPT" to "application/vnd.ms-powerpoint",
                        "PPTX" to "application/vnd.openxmlformats-officedocument.presentationml.presentation",
                        "MP4" to "video/mp4",
                        "AVI" to "video/x-msvideo",
                    ),
                    6L,
                ),
            )
        }

        private val mockUserId = "de2192de-9b01-11ed-988c-0aee35cf62c8"
        private val classroomId = "640ce7fb-f25a-41f5-8e68-f83d7e573a95"
        private val foldId = "facdb001-d280-4fec-a634-1bc804dee543"
        private val fileId = "a85c9cff-cdaa-4816-a488-067f73d970c8"
        private var file =
            MockMultipartFile(
                "file",
                "fileName.jpg",
                MediaType.APPLICATION_JSON_VALUE,
                "content".toByteArray(),
            )

        private val updateClassroomFileNameRequest =
            UpdateClassroomFileNameRequest(
                name = "newName",
            )

        private val updateClassroomFileStateRequest =
            UpdateClassroomFileStateRequest(
                state = ClassroomFileState.PUBLISHED,
            )

        @Test
        @WithMockUser(username = "de2192de-9b01-11ed-988c-0aee35cf62c8", roles = ["TEACHER"])
        internal fun `should return 200 when get classroom files`() {
            val expectedFolderPath =
                listOf(
                    ClassroomFile(
                        id = "rootId",
                        name = "rootName",
                        state = ClassroomFileState.PUBLISHED,
                        type = ClassroomFileType.FOLDER,
                        deleted = false,
                        uploadedBy = mockUserId,
                        rootFolder = true,
                        classroomId = classroomId,
                        createdAt = LocalDateTime.of(2022, 3, 14, 15, 16, 17),
                    ),
                )

            val expectedChildrenFiles =
                listOf(
                    ClassroomFile(
                        id = "id",
                        s3File =
                            S3File(
                                "fileId",
                                "pdf",
                                "bucket",
                                "objectKey",
                            ),
                        name = "file001",
                        parentId = "rootId",
                        state = ClassroomFileState.PUBLISHED,
                        type = ClassroomFileType.FILE,
                        deleted = false,
                        uploadedBy = mockUserId,
                        rootFolder = false,
                        classroomId = classroomId,
                        publishedAt = LocalDateTime.of(2022, 3, 15, 15, 16, 17),
                        createdAt = LocalDateTime.of(2022, 3, 14, 15, 16, 17),
                    ),
                )

            whenever(classroomFileService.getParentFolderPathByFolderId(classroomId, foldId)).thenReturn(expectedFolderPath)
            whenever(classroomFileService.getChildrenFilesByFolderId(classroomId, foldId)).thenReturn(expectedChildrenFiles)

            mockMvc
                .perform(
                    get("/classrooms/640ce7fb-f25a-41f5-8e68-f83d7e573a95/files/facdb001-d280-4fec-a634-1bc804dee543")
                        .contentType(MediaType.APPLICATION_JSON),
                )
                .andExpect(status().isOk)
                .andExpect(jsonPath("$.folderPath[0].id").value("rootId"))
                .andExpect(jsonPath("$.folderPath[0].s3FileId").value(null))
                .andExpect(jsonPath("$.folderPath[0].name").value("rootName"))
                .andExpect(jsonPath("$.folderPath[0].state").value(ClassroomFileState.PUBLISHED.name))
                .andExpect(jsonPath("$.folderPath[0].type").value(ClassroomFileType.FOLDER.name))
                .andExpect(jsonPath("$.folderPath[0].uploadedBy").value(mockUserId))
                .andExpect(jsonPath("$.folderPath[0].uploadedAt").value(1647270977L))
                .andExpect(jsonPath("$.childrenFiles[0].id").value("id"))
                .andExpect(jsonPath("$.childrenFiles[0].s3FileId").value("fileId"))
                .andExpect(jsonPath("$.childrenFiles[0].fileExtension").value("pdf"))
                .andExpect(jsonPath("$.childrenFiles[0].name").value("file001"))
                .andExpect(jsonPath("$.childrenFiles[0].state").value(ClassroomFileState.PUBLISHED.name))
                .andExpect(jsonPath("$.childrenFiles[0].type").value(ClassroomFileType.FILE.name))
                .andExpect(jsonPath("$.childrenFiles[0].uploadedBy").value(mockUserId))
                .andExpect(jsonPath("$.childrenFiles[0].publishedAt").value(1647357377L))
                .andExpect(jsonPath("$.childrenFiles[0].uploadedAt").value(1647270977L))
        }

        @Test
        @WithMockUser(username = "de2192de-9b01-11ed-988c-0aee35cf62c8", roles = ["TEACHER"])
        internal fun `should return 403 when current user does not have permission to read classroom file`() {
            whenever(classroomService.hasPermissionToReadOrUpdateClassroom(classroomId, mockUserId)).thenReturn(false)

            mockMvc
                .perform(
                    get("/classrooms/640ce7fb-f25a-41f5-8e68-f83d7e573a95/files/facdb001-d280-4fec-a634-1bc804dee543")
                        .contentType(MediaType.APPLICATION_JSON),
                )
                .andExpect(status().isForbidden)
        }

        @Test
        @WithMockUser(username = "de2192de-9b01-11ed-988c-0aee35cf62c8", roles = ["TEACHER"])
        internal fun `should return 200 when update classroom file name successfully`() {
            whenever(classroomService.hasPermissionToReadOrUpdateClassroom(classroomId, mockUserId)).thenReturn(true)
            doNothing().whenever(classroomFileService).updateClassroomFileName(
                classroomId,
                foldId,
                "clean-name",
            )

            mockMvc
                .perform(
                    patch("/classrooms/$classroomId/files/$foldId/name")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(updateClassroomFileNameRequest)),
                )
                .andExpect(status().isOk)

            verify(classroomFileService, times(1)).updateClassroomFileName(
                classroomId,
                foldId,
                "clean-name",
            )
        }

        @Test
        @WithMockUser(username = "de2192de-9b01-11ed-988c-0aee35cf62c8", roles = ["TEACHER"])
        internal fun `should return 403 when current user does not have permission to update classroom file name`() {
            whenever(classroomService.hasPermissionToReadOrUpdateClassroom(classroomId, mockUserId)).thenReturn(false)

            mockMvc
                .perform(
                    patch("/classrooms/$classroomId/files/$foldId/name")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(updateClassroomFileNameRequest)),
                )
                .andExpect(status().isForbidden)
        }

        @Test
        @WithMockUser(username = "de2192de-9b01-11ed-988c-0aee35cf62c8", roles = ["TEACHER"])
        internal fun `should return 200 when update classroom file state successfully`() {
            whenever(classroomService.hasPermissionToReadOrUpdateClassroom(classroomId, mockUserId)).thenReturn(true)
            doNothing().whenever(classroomFileService).updateClassroomFileState(
                classroomId,
                foldId,
                ClassroomFileState.PUBLISHED,
            )

            mockMvc
                .perform(
                    patch("/classrooms/$classroomId/files/$foldId/state")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(updateClassroomFileStateRequest)),
                )
                .andExpect(status().isOk)

            verify(classroomFileService, times(1)).updateClassroomFileState(
                classroomId,
                foldId,
                ClassroomFileState.PUBLISHED,
            )
        }

        @Test
        @WithMockUser(username = "de2192de-9b01-11ed-988c-0aee35cf62c8", roles = ["TEACHER"])
        internal fun `should return 403 when current user does not have permission to update classroom file state`() {
            whenever(classroomService.hasPermissionToReadOrUpdateClassroom(classroomId, mockUserId)).thenReturn(false)

            mockMvc
                .perform(
                    patch("/classrooms/$classroomId/files/$foldId/state")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(updateClassroomFileStateRequest)),
                )
                .andExpect(status().isForbidden)
        }

        @Test
        @WithMockUser(username = "de2192de-9b01-11ed-988c-0aee35cf62c8", roles = ["TEACHER"])
        internal fun `should return 403 when current user does not have permission to delete classroom file`() {
            whenever(classroomService.hasPermissionToReadOrUpdateClassroom(classroomId, mockUserId)).thenReturn(false)

            mockMvc
                .perform(
                    delete("/classrooms/$classroomId/files/$foldId")
                        .contentType(MediaType.APPLICATION_JSON),
                )
                .andExpect(status().isForbidden)
        }

        @Test
        @WithMockUser(username = "de2192de-9b01-11ed-988c-0aee35cf62c8", roles = ["TEACHER"])
        internal fun `should return 200 when delete classroom file successfully`() {
            whenever(classroomService.hasPermissionToReadOrUpdateClassroom(classroomId, mockUserId)).thenReturn(true)
            doNothing().whenever(classroomFileService).deleteClassroomFile(
                classroomId,
                foldId,
            )
            mockMvc
                .perform(
                    delete("/classrooms/$classroomId/files/$foldId")
                        .contentType(MediaType.APPLICATION_JSON),
                )
                .andExpect(status().isOk)
        }

        @Test
        @WithMockUser(username = "de2192de-9b01-11ed-988c-0aee35cf62c8", roles = ["TEACHER"])
        internal fun `should return 201 when create classroom files and type file`() {
            val request =
                CreateClassroomFileRequest(
                    s3FileId = "fileId",
                    name = "name",
                    currentFolderId = "foldId",
                    type = ClassroomFileType.FILE,
                )
            val classroomFileFromRequest = request.xssFilter(xssFilter).toClassroomFile(classroomId, mockUserId)

            whenever(classroomFileService.createClassroomFileOrFolder(classroomFileFromRequest.copy(name = "clean-name"), "fileId")).thenReturn(
                ClassroomFile(
                    id = "id",
                    S3File(
                        "fileId",
                        "pdf",
                        "mockBucket",
                        "mockObjectKey",
                    ),
                    name = "name",
                    parentId = "foldId",
                    state = ClassroomFileState.UNPUBLISHED,
                    type = ClassroomFileType.FILE,
                    deleted = false,
                    rootFolder = false,
                    uploadedBy = mockUserId,
                    classroomId = classroomId,
                    createdAt = LocalDateTime.of(2022, 3, 14, 15, 16, 17),
                ),
            )
            mockMvc
                .perform(
                    post("/classrooms/640ce7fb-f25a-41f5-8e68-f83d7e573a95/files")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)),
                )
                .andExpect(status().isCreated)
                .andExpect(jsonPath("$.id").value("id"))
                .andExpect(jsonPath("$.s3FileId").value("fileId"))
                .andExpect(jsonPath("$.fileExtension").value("pdf"))
                .andExpect(jsonPath("$.name").value("name"))
                .andExpect(jsonPath("$.state").value(ClassroomFileState.UNPUBLISHED.name))
                .andExpect(jsonPath("$.type").value(ClassroomFileType.FILE.name))
                .andExpect(jsonPath("$.uploadedBy").value(mockUserId))
                .andExpect(jsonPath("$.uploadedAt").value(1647270977L))
        }

        @Test
        @WithMockUser(username = "de2192de-9b01-11ed-988c-0aee35cf62c8", roles = ["TEACHER"])
        internal fun `should return 201 when create classroom files and type folder`() {
            val request =
                CreateClassroomFileRequest(
                    name = "name",
                    currentFolderId = "foldId",
                    type = ClassroomFileType.FOLDER,
                )
            val classroomFileFromRequest = request.toClassroomFile(classroomId, mockUserId)

            whenever(classroomFileService.createClassroomFileOrFolder(classroomFileFromRequest.copy(name = "clean-name"), null)).thenReturn(
                ClassroomFile(
                    id = "id",
                    name = "name",
                    parentId = "foldId",
                    state = ClassroomFileState.UNPUBLISHED,
                    type = ClassroomFileType.FOLDER,
                    deleted = false,
                    rootFolder = false,
                    uploadedBy = mockUserId,
                    classroomId = classroomId,
                    createdAt = LocalDateTime.of(2022, 3, 14, 15, 16, 17),
                ),
            )
            mockMvc
                .perform(
                    post("/classrooms/640ce7fb-f25a-41f5-8e68-f83d7e573a95/files")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(request)),
                )
                .andExpect(status().isCreated)
                .andExpect(jsonPath("$.id").value("id"))
                .andExpect(jsonPath("$.name").value("name"))
                .andExpect(jsonPath("$.state").value(ClassroomFileState.UNPUBLISHED.name))
                .andExpect(jsonPath("$.type").value(ClassroomFileType.FOLDER.name))
                .andExpect(jsonPath("$.uploadedBy").value(mockUserId))
                .andExpect(jsonPath("$.uploadedAt").value(1647270977L))
        }

        @Test
        @WithMockUser(username = "de2192de-9b01-11ed-988c-0aee35cf62c8", roles = ["TEACHER"])
        internal fun `throw exception when create classroom files with invalid mime type file`() {
            val file =
                MockMultipartFile(
                    "file",
                    "fileName.jpegss",
                    MediaType.APPLICATION_JSON_VALUE,
                    "content".toByteArray(),
                )
            mockMvc
                .perform(
                    MockMvcRequestBuilders.multipart("/classrooms/640ce7fb-f25a-41f5-8e68-f83d7e573a95//s3-files/upload").file(file).accept(MediaType.APPLICATION_JSON),
                )
                .andExpect(status().isBadRequest)
                .andExpect(jsonPath("$.message[0]").value("Invalid Classroom file extension jpegss"))
        }

        @Test
        @WithMockUser(username = "de2192de-9b01-11ed-988c-0aee35cf62c8", roles = ["TEACHER"])
        internal fun `throw exception when create classroom files with invalid file size`() {
            whenever(s3Properties.classroomFile).thenReturn(
                S3ClassroomFileProperties(
                    "test-marsladder-classroom-files",
                    0L,
                    mapOf(
                        "PNG" to "image/jpeg",
                        "JPG" to "image/jpeg",
                        "JPEG" to "image/jpeg",
                        "PDF" to "application/pdf",
                        "DOC" to "application/msword",
                        "DOCX" to "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                        "PPT" to "application/vnd.ms-powerpoint",
                        "PPTX" to "application/vnd.openxmlformats-officedocument.presentationml.presentation",
                        "MP4" to "video/mp4",
                        "AVI" to "video/x-msvideo",
                    ),
                    6L,
                ),
            )
            mockMvc
                .perform(
                    MockMvcRequestBuilders.multipart("/classrooms/640ce7fb-f25a-41f5-8e68-f83d7e573a95//s3-files/upload").file(file).accept(MediaType.APPLICATION_JSON),
                )
                .andExpect(status().isBadRequest)
                .andExpect(jsonPath("$.message[0]").value("File too large."))
        }

        @Test
        @WithMockUser(username = "de2192de-9b01-11ed-988c-0aee35cf62c8", roles = ["TEACHER"])
        internal fun `should return 201 when upload file`() {
            whenever(classroomFileService.uploadClassroomFile(classroomId, file)).thenReturn(
                S3File(
                    id = "id",
                    extension = "pdf",
                    bucket = "bucket",
                    objectKey = "objectKey",
                ),
            )
            mockMvc
                .perform(
                    MockMvcRequestBuilders.multipart("/classrooms/640ce7fb-f25a-41f5-8e68-f83d7e573a95//s3-files/upload").file(file).accept(MediaType.APPLICATION_JSON),
                )
                .andExpect(status().isCreated)
                .andExpect(jsonPath("$.s3FileId").value("id"))
        }

        @Test
        @WithMockUser(username = "de2192de-9b01-11ed-988c-0aee35cf62c8", roles = ["TEACHER"])
        internal fun `should return 200 when remove file`() {
            val fileId = "fileId"
            val targetFolderId = "targetFolderId"
            doNothing().whenever(classroomFileService).moveClassroomFile(
                classroomId,
                fileId,
                targetFolderId,
            )
            mockMvc
                .perform(
                    patch("/classrooms/$classroomId/files/$fileId/move")
                        .contentType(MediaType.APPLICATION_JSON)
                        .param("targetFolderId", targetFolderId),
                )
                .andExpect(status().isOk)

            verify(classroomFileService, times(1)).moveClassroomFile(
                classroomId,
                fileId,
                targetFolderId,
            )
        }

        @Test
        @WithMockUser(username = "de2192de-9b01-11ed-988c-0aee35cf62c8", roles = ["TEACHER"])
        internal fun `should return 200 with folder structure when user has permission`() {
            val classroomFolderStructure =
                ClassroomFolderStructure(
                    id = "facdb001-d280-4fec-a634-1bc804dee543",
                    name = "Folder Name",
                    childrenFolders =
                        listOf(
                            ClassroomFolderStructure(
                                id = "child-folder-id",
                                name = "Child Folder",
                                childrenFolders = emptyList(),
                            ),
                        ),
                )

            whenever(classroomService.hasPermissionToReadOrUpdateClassroom(classroomId, mockUserId)).thenReturn(true)
            whenever(classroomFileService.getFolderStructure(classroomId, null)).thenReturn(classroomFolderStructure)

            mockMvc
                .perform(
                    get("/classrooms/$classroomId/folder-structure")
                        .contentType(MediaType.APPLICATION_JSON),
                )
                .andExpect(status().isOk)
                .andExpect(jsonPath("$.id").value("facdb001-d280-4fec-a634-1bc804dee543"))
                .andExpect(jsonPath("$.name").value("Folder Name"))
                .andExpect(jsonPath("$.childrenFolders[0].id").value("child-folder-id"))
                .andExpect(jsonPath("$.childrenFolders[0].name").value("Child Folder"))
                .andExpect(jsonPath("$.childrenFolders[0].childrenFolders").isEmpty)
        }

        @Test
        @WithMockUser(username = "de2192de-9b01-11ed-988c-0aee35cf62c8", roles = ["TEACHER"])
        internal fun `should return 403 when user does not have permission to access folder structure`() {
            whenever(classroomService.hasPermissionToReadOrUpdateClassroom(classroomId, mockUserId)).thenReturn(false)

            mockMvc
                .perform(
                    get("/classrooms/$classroomId/folder-structure")
                        .contentType(MediaType.APPLICATION_JSON),
                )
                .andExpect(status().isForbidden)
        }

        @Test
        @WithMockUser(username = "de2192de-9b01-11ed-988c-0aee35cf62c8", roles = ["TEACHER"])
        internal fun `should return 200 when current get classroom file detail`() {
            whenever(classroomFileService.getClassroomFileDetails(fileId))
                .thenReturn(
                    ClassroomFile(
                        id = "id",
                        S3File(
                            "fileId",
                            "pdf",
                            "mockBucket",
                            "mockObjectKey",
                        ),
                        name = "name",
                        parentId = "foldId",
                        state = ClassroomFileState.PUBLISHED,
                        type = ClassroomFileType.FILE,
                        deleted = false,
                        rootFolder = false,
                        uploadedBy = mockUserId,
                        classroomId = classroomId,
                        createdAt = LocalDateTime.of(2022, 3, 14, 15, 16, 17),
                    ),
                )

            mockMvc
                .perform(
                    get("/classroom-files/$fileId")
                        .contentType(MediaType.APPLICATION_JSON),
                )
                .andExpect(status().isOk)
                .andExpect(jsonPath("$.id").value("id"))
                .andExpect(jsonPath("$.s3FileId").value("fileId"))
                .andExpect(jsonPath("$.fileExtension").value("pdf"))
                .andExpect(jsonPath("$.name").value("name"))
                .andExpect(jsonPath("$.state").value(ClassroomFileState.PUBLISHED.name))
                .andExpect(jsonPath("$.type").value(ClassroomFileType.FILE.name))
                .andExpect(jsonPath("$.uploadedBy").value(mockUserId))
                .andExpect(jsonPath("$.uploadedAt").value(1647270977L))
        }
    }
