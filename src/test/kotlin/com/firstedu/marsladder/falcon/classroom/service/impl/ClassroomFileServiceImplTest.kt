package com.firstedu.marsladder.falcon.classroom.service.impl

import com.firstedu.marsladder.falcon.classroom.ClassroomFileState
import com.firstedu.marsladder.falcon.classroom.ClassroomFileType
import com.firstedu.marsladder.falcon.classroom.ClassroomStatus
import com.firstedu.marsladder.falcon.classroom.repository.ClassroomFileRepository
import com.firstedu.marsladder.falcon.classroom.repository.entity.ClassroomFileEntity
import com.firstedu.marsladder.falcon.classroom.service.ClassroomService
import com.firstedu.marsladder.falcon.classroom.service.ClassroomTeacherService
import com.firstedu.marsladder.falcon.classroom.service.domain.Classroom
import com.firstedu.marsladder.falcon.classroom.service.domain.ClassroomFile
import com.firstedu.marsladder.falcon.classroom.service.exception.ClassroomFileNotFoundException
import com.firstedu.marsladder.falcon.classroom.service.exception.InvalidClassroomFileException
import com.firstedu.marsladder.falcon.classroom.service.exception.InvalidClassroomFolderException
import com.firstedu.marsladder.falcon.config.S3ClassroomFileProperties
import com.firstedu.marsladder.falcon.config.S3Properties
import com.firstedu.marsladder.falcon.course.repository.CourseRepository
import com.firstedu.marsladder.falcon.course.repository.entity.CourseEntity
import com.firstedu.marsladder.falcon.file.repository.S3FileRepository
import com.firstedu.marsladder.falcon.file.repository.entity.S3FileEntity
import com.firstedu.marsladder.falcon.file.service.domain.S3File
import com.firstedu.marsladder.falcon.infrastructure.aws.s3.ObjectClient
import com.firstedu.marsladder.falcon.utils.DateTimeUtil
import com.nhaarman.mockitokotlin2.any
import com.nhaarman.mockitokotlin2.verify
import com.nhaarman.mockitokotlin2.whenever
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.ArgumentCaptor
import org.mockito.Captor
import org.mockito.InjectMocks
import org.mockito.Mock
import org.mockito.Mockito.times
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.junit.jupiter.MockitoSettings
import org.mockito.quality.Strictness
import org.springframework.http.MediaType
import org.springframework.mock.web.MockMultipartFile
import java.time.LocalDateTime
import java.util.Optional

@ExtendWith(MockitoExtension::class)
@MockitoSettings(strictness = Strictness.LENIENT)
internal class ClassroomFileServiceImplTest {
    @Mock
    private lateinit var classroomFileRepository: ClassroomFileRepository

    @Mock
    private lateinit var courseRepository: CourseRepository

    @Mock
    private lateinit var objectClient: ObjectClient

    @Mock
    private lateinit var s3Properties: S3Properties

    @Mock
    private lateinit var classroomTeacherService: ClassroomTeacherService

    @Mock
    private lateinit var s3FileRepository: S3FileRepository

    @Mock
    private lateinit var classroomService: ClassroomService

    @Mock
    private lateinit var dateTimeUtil: DateTimeUtil

    @Captor
    private lateinit var s3FileEntityCaptor: ArgumentCaptor<S3FileEntity>

    @InjectMocks
    private lateinit var classroomFileServiceImpl: ClassroomFileServiceImpl

    @Captor
    private lateinit var classroomFileCapture: ArgumentCaptor<ClassroomFileEntity>

    private val mockUserId = "de2192de-9b01-11ed-988c-0aee35cf62c8"
    private val classroomId = "640ce7fb-f25a-41f5-8e68-f83d7e573a95"

    private val nowTime = LocalDateTime.of(2024, 1, 1, 1, 1, 1)

    @BeforeEach
    internal fun setUp() {
        whenever(classroomService.getClassroomById(classroomId))
            .thenReturn(
                Classroom(
                    id = "fakeId",
                    name = "fakeName",
                    subjectId = "fakeSubjectId",
                    courseId = "courseId",
                    status = ClassroomStatus.DISMISSED,
                    label = "fakeLabel",
                    teacherUserId = mockUserId,
                    description = "description",
                    emptyNotified = true,
                    createdAt = LocalDateTime.of(2022, 3, 14, 15, 16, 17),
                ),
            )
        whenever(classroomTeacherService.getClassroomTeacherOrTutorName(classroomId, mockUserId))
            .thenReturn("TeacherName")
        whenever(s3Properties.classroomFile).thenReturn(
            S3ClassroomFileProperties(
                "test-marsladder-classroom-files",
                50 * 1024 * 1024L,
                mapOf(
                    "PNG" to "image/jpeg",
                    "JPG" to "image/jpeg",
                    "JPEG" to "image/jpeg",
                    "PDF" to "application/pdf",
                    "DOC" to "application/msword",
                    "DOCX" to "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                    "PPT" to "application/vnd.ms-powerpoint",
                    "PPTX" to "application/vnd.openxmlformats-officedocument.presentationml.presentation",
                    "MP4" to "video/mp4",
                    "AVI" to "video/x-msvideo",
                ),
                6L,
            ),
        )

        whenever(dateTimeUtil.getNowTime()).thenReturn(nowTime)
    }

    @Test
    internal fun `get children file by folder id successfully`() {
        val fileEntity =
            ClassroomFileEntity(
                id = "id",
                name = "fileName",
                type = ClassroomFileType.FILE,
                s3FileEntity =
                    S3FileEntity(
                        id = "file_id",
                        extension = "jpg",
                        bucket = "bucket",
                        objectKey = "key",
                        createdAt = LocalDateTime.of(2022, 3, 14, 15, 16, 17),
                    ),
                parentId = "rootId",
                uploadedBy = mockUserId,
                classroomId = classroomId,
                state = ClassroomFileState.PUBLISHED,
                createdAt = LocalDateTime.of(2022, 3, 14, 15, 16, 17),
            )

        val classroomFileEntityList = listOf(fileEntity)
        whenever(classroomFileRepository.findByClassroomIdAndParentIdAndDeleted(classroomId, "rootId", false)).thenReturn(classroomFileEntityList)

        val expectedClassroomFileList = classroomFileServiceImpl.getChildrenFilesByFolderId(classroomId, "rootId")
        assertEquals(expectedClassroomFileList.first().id, "id")
        assertEquals(expectedClassroomFileList.first().s3File!!.id, "file_id")
        assertEquals(expectedClassroomFileList.first().name, "fileName")
        assertEquals(expectedClassroomFileList.first().parentId, "rootId")
        assertEquals(expectedClassroomFileList.first().state, ClassroomFileState.PUBLISHED)
        assertEquals(expectedClassroomFileList.first().type, ClassroomFileType.FILE)
        assertEquals(expectedClassroomFileList.first().deleted, false)
        assertEquals(expectedClassroomFileList.first().uploadedBy, "TeacherName")
        assertEquals(expectedClassroomFileList.first().rootFolder, false)
    }

    @Test
    internal fun `get parent folder by path successfully`() {
        val rootFolderEntity =
            ClassroomFileEntity(
                id = "rootId",
                name = "rootName",
                type = ClassroomFileType.FOLDER,
                uploadedBy = mockUserId,
                classroomId = classroomId,
                rootFolder = true,
                state = ClassroomFileState.PUBLISHED,
                createdAt = LocalDateTime.of(2022, 3, 14, 15, 16, 17),
            )
        whenever(classroomFileRepository.findByClassroomIdAndIdAndDeleted(classroomId, "root", false)).thenReturn(rootFolderEntity)

        whenever(classroomFileRepository.findByClassroomIdAndIdAndDeleted(classroomId, "root1", false)).thenReturn(
            ClassroomFileEntity(
                id = "root1",
                name = "root1Name",
                parentId = "root",
                type = ClassroomFileType.FOLDER,
                uploadedBy = mockUserId,
                classroomId = classroomId,
                state = ClassroomFileState.PUBLISHED,
                createdAt = LocalDateTime.of(2022, 3, 14, 15, 16, 17),
            ),
        )

        val expectedClassroomFileList = classroomFileServiceImpl.getParentFolderPathByFolderId(classroomId, "root1")
        assertEquals(expectedClassroomFileList.first().id, "rootId")
        assertEquals(expectedClassroomFileList.first().name, "rootName")
        assertEquals(expectedClassroomFileList.first().parentId, null)
        assertEquals(expectedClassroomFileList.first().rootFolder, true)
        assertEquals(expectedClassroomFileList.first().state, ClassroomFileState.PUBLISHED)
        assertEquals(expectedClassroomFileList.first().type, ClassroomFileType.FOLDER)

        assertEquals(expectedClassroomFileList.get(1).id, "root1")
        assertEquals(expectedClassroomFileList.get(1).name, "root1Name")
        assertEquals(expectedClassroomFileList.get(1).parentId, "root")
        assertEquals(expectedClassroomFileList.get(1).state, ClassroomFileState.PUBLISHED)
        assertEquals(expectedClassroomFileList.get(1).type, ClassroomFileType.FOLDER)
    }

    @Test
    internal fun `update classroom file name type folder successfully`() {
        val classroomFile =
            ClassroomFileEntity(
                id = "root1",
                name = "root1Name",
                parentId = "root",
                type = ClassroomFileType.FOLDER,
                uploadedBy = mockUserId,
                classroomId = classroomId,
                state = ClassroomFileState.PUBLISHED,
                createdAt = LocalDateTime.of(2022, 3, 14, 15, 16, 17),
            )
        whenever(classroomFileRepository.findByClassroomIdAndIdAndRootFolder(classroomId, "root1", false)).thenReturn(
            classroomFile,
        )
        whenever(classroomFileRepository.save(classroomFile)).thenReturn(
            classroomFile,
        )

        classroomFileServiceImpl.updateClassroomFileName(classroomId, "root1", "newName")

        verify(classroomFileRepository, times(1)).save(classroomFileCapture.capture())
        assertEquals(classroomFileCapture.value.name, "newName")
    }

    @Test
    internal fun `update classroom file name type file successfully`() {
        val classroomFile =
            ClassroomFileEntity(
                id = "id",
                name = "file",
                S3FileEntity(
                    id = "fileId",
                    extension = "jpg",
                    bucket = "bucket",
                    objectKey = "objectkey",
                ),
                parentId = "root",
                type = ClassroomFileType.FILE,
                uploadedBy = mockUserId,
                classroomId = classroomId,
                state = ClassroomFileState.PUBLISHED,
                createdAt = LocalDateTime.of(2022, 3, 14, 15, 16, 17),
            )
        whenever(classroomFileRepository.findByClassroomIdAndIdAndRootFolder(classroomId, "id", false)).thenReturn(
            classroomFile,
        )
        whenever(classroomFileRepository.save(classroomFile)).thenReturn(
            classroomFile,
        )

        classroomFileServiceImpl.updateClassroomFileName(classroomId, "id", "file.jpg")

        verify(classroomFileRepository, times(1)).save(classroomFileCapture.capture())
        assertEquals(classroomFileCapture.value.name, "file")
    }

    @Test
    internal fun `update classroom file name type file with no extension successfully`() {
        val classroomFile =
            ClassroomFileEntity(
                id = "id",
                name = "file",
                S3FileEntity(
                    id = "fileId",
                    extension = "jpg",
                    bucket = "bucket",
                    objectKey = "objectkey",
                ),
                parentId = "root",
                type = ClassroomFileType.FILE,
                uploadedBy = mockUserId,
                classroomId = classroomId,
                state = ClassroomFileState.PUBLISHED,
                createdAt = LocalDateTime.of(2022, 3, 14, 15, 16, 17),
            )
        whenever(classroomFileRepository.findByClassroomIdAndIdAndRootFolder(classroomId, "id", false)).thenReturn(
            classroomFile,
        )
        whenever(classroomFileRepository.save(classroomFile)).thenReturn(
            classroomFile,
        )

        classroomFileServiceImpl.updateClassroomFileName(classroomId, "id", "file")

        verify(classroomFileRepository, times(1)).save(classroomFileCapture.capture())
        assertEquals(classroomFileCapture.value.name, "file")
    }

    @Test
    internal fun `update classroom file name type file with different extension successfully`() {
        val classroomFile =
            ClassroomFileEntity(
                id = "id",
                name = "file.pdf",
                S3FileEntity(
                    id = "fileId",
                    extension = "jpg",
                    bucket = "bucket",
                    objectKey = "objectkey",
                ),
                parentId = "root",
                type = ClassroomFileType.FILE,
                uploadedBy = mockUserId,
                classroomId = classroomId,
                state = ClassroomFileState.PUBLISHED,
                createdAt = LocalDateTime.of(2022, 3, 14, 15, 16, 17),
            )
        whenever(classroomFileRepository.findByClassroomIdAndIdAndRootFolder(classroomId, "id", false)).thenReturn(
            classroomFile,
        )
        whenever(classroomFileRepository.save(classroomFile)).thenReturn(
            classroomFile,
        )

        classroomFileServiceImpl.updateClassroomFileName(classroomId, "id", "file.pdf.jpg")

        verify(classroomFileRepository, times(1)).save(classroomFileCapture.capture())
        assertEquals(classroomFileCapture.value.name, "file.pdf")
    }

    @Test
    internal fun `throw ClassroomFileNotFoundException when update classroom file name and classroom file is not existed`() {
        whenever(classroomFileRepository.findByClassroomIdAndIdAndRootFolder(classroomId, "root1", false)).thenReturn(
            null,
        )

        assertThrows<ClassroomFileNotFoundException> {
            classroomFileServiceImpl.updateClassroomFileName(classroomId, "root1", "newName")
        }
    }

    @Nested
    inner class UpdateFileStateOrRemoveFile {
        /**
         *                               level3-folder   level4-file
         *                level2-folder
         * level1-folder                 level3-file
         *                level2-file
         */

        private val level2FolderEntity =
            ClassroomFileEntity(
                id = "level2Folder",
                name = "level2Folder",
                parentId = "level1Folder",
                type = ClassroomFileType.FOLDER,
                uploadedBy = mockUserId,
                classroomId = classroomId,
                state = ClassroomFileState.UNPUBLISHED,
            )

        private val level2FileEntity =
            ClassroomFileEntity(
                id = "level2File",
                name = "level2File",
                parentId = "level1Folder",
                type = ClassroomFileType.FILE,
                classroomId = classroomId,
                state = ClassroomFileState.UNPUBLISHED,
            )

        private val level1FolderEntity =
            ClassroomFileEntity(
                id = "level1Folder",
                name = "level1Folder",
                type = ClassroomFileType.FOLDER,
                uploadedBy = mockUserId,
                classroomId = classroomId,
                state = ClassroomFileState.UNPUBLISHED,
            )

        private val level3FolderEntity =
            ClassroomFileEntity(
                id = "level3Folder",
                name = "level3Folder",
                parentId = "level2Folder",
                type = ClassroomFileType.FOLDER,
                classroomId = classroomId,
                state = ClassroomFileState.UNPUBLISHED,
            )

        private val level4FileEntity =
            ClassroomFileEntity(
                id = "level4File",
                name = "level4File",
                parentId = "level3Folder",
                type = ClassroomFileType.FILE,
                classroomId = classroomId,
                state = ClassroomFileState.UNPUBLISHED,
            )

        private val level3FileEntity =
            ClassroomFileEntity(
                id = "level3File",
                name = "level3File",
                parentId = "level2Folder",
                type = ClassroomFileType.FILE,
                classroomId = classroomId,
                state = ClassroomFileState.UNPUBLISHED,
            )

        @Test
        internal fun `update classroom folder state publish successfully`() {
            whenever(classroomFileRepository.findByClassroomIdAndIdAndRootFolder(classroomId, "level2Folder", false)).thenReturn(
                level2FolderEntity,
            )

            whenever(classroomFileRepository.findByClassroomIdAndIdAndDeleted(classroomId, "level2Folder", false)).thenReturn(
                level2FolderEntity,
            )
            whenever(classroomFileRepository.save(level2FolderEntity.copy(state = ClassroomFileState.PUBLISHED, publishedAt = nowTime)))
                .thenReturn(level2FolderEntity.copy(state = ClassroomFileState.PUBLISHED, publishedAt = nowTime))

            whenever(classroomFileRepository.findByClassroomIdAndIdAndDeleted(classroomId, "level1Folder", false)).thenReturn(
                level1FolderEntity,
            )
            whenever(classroomFileRepository.save(level1FolderEntity.copy(state = ClassroomFileState.PUBLISHED, publishedAt = nowTime)))
                .thenReturn(level1FolderEntity.copy(state = ClassroomFileState.PUBLISHED, publishedAt = nowTime))

            whenever(classroomFileRepository.findByClassroomIdAndParentIdAndDeleted(classroomId, "level2Folder", false)).thenReturn(
                listOf(level3FileEntity, level3FolderEntity),
            )
            whenever(classroomFileRepository.findByClassroomIdAndParentIdAndDeleted(classroomId, "level3Folder", false)).thenReturn(
                listOf(level4FileEntity),
            )

            classroomFileServiceImpl.updateClassroomFileState(classroomId, "level2Folder", ClassroomFileState.PUBLISHED)
            verify(classroomFileRepository).save(level2FolderEntity.copy(state = ClassroomFileState.PUBLISHED, publishedAt = nowTime))
            verify(classroomFileRepository).save(level1FolderEntity.copy(state = ClassroomFileState.PUBLISHED, publishedAt = nowTime))
            verify(classroomFileRepository).save(level3FolderEntity.copy(state = ClassroomFileState.PUBLISHED, publishedAt = nowTime))
            verify(classroomFileRepository).save(level3FileEntity.copy(state = ClassroomFileState.PUBLISHED, publishedAt = nowTime))
            verify(classroomFileRepository).save(level4FileEntity.copy(state = ClassroomFileState.PUBLISHED, publishedAt = nowTime))
        }

        @Test
        internal fun `update classroom folder state unPublish successfully`() {
            buildPublishedClassroomFileStructure()
            whenever(classroomFileRepository.findByClassroomIdAndIdAndRootFolder(classroomId, "level2Folder", false)).thenReturn(
                level2FolderEntity,
            )
            whenever(classroomFileRepository.findByClassroomIdAndIdAndDeleted(classroomId, "level2Folder", false)).thenReturn(
                level2FolderEntity,
            )
            whenever(classroomFileRepository.save(level2FolderEntity.copy(state = ClassroomFileState.UNPUBLISHED, publishedAt = nowTime)))
                .thenReturn(level2FolderEntity.copy(state = ClassroomFileState.PUBLISHED, publishedAt = nowTime))

            whenever(classroomFileRepository.findByClassroomIdAndParentIdAndDeleted(classroomId, "level2Folder", false)).thenReturn(
                listOf(level3FileEntity, level3FolderEntity),
            )
            whenever(classroomFileRepository.findByClassroomIdAndParentIdAndDeleted(classroomId, "level3Folder", false)).thenReturn(
                listOf(level4FileEntity),
            )

            classroomFileServiceImpl.updateClassroomFileState(classroomId, "level2Folder", ClassroomFileState.UNPUBLISHED)
            verify(classroomFileRepository).save(level3FolderEntity.copy(state = ClassroomFileState.UNPUBLISHED, publishedAt = nowTime))
            verify(classroomFileRepository).save(level3FileEntity.copy(state = ClassroomFileState.UNPUBLISHED, publishedAt = nowTime))
            verify(classroomFileRepository).save(level4FileEntity.copy(state = ClassroomFileState.UNPUBLISHED, publishedAt = nowTime))
            verify(classroomFileRepository).save(level2FolderEntity.copy(state = ClassroomFileState.UNPUBLISHED, publishedAt = nowTime))
        }

        @Test
        internal fun `update classroom file state publish successfully`() {
            whenever(classroomFileRepository.findByClassroomIdAndIdAndRootFolder(classroomId, "level2File", false)).thenReturn(
                level2FileEntity,
            )
            whenever(classroomFileRepository.findByClassroomIdAndIdAndDeleted(classroomId, "level2File", false)).thenReturn(
                level2FileEntity,
            )
            whenever(classroomFileRepository.save(level2FileEntity.copy(state = ClassroomFileState.PUBLISHED, publishedAt = nowTime)))
                .thenReturn(level2FileEntity.copy(state = ClassroomFileState.PUBLISHED, publishedAt = nowTime))

            whenever(classroomFileRepository.findByClassroomIdAndIdAndDeleted(classroomId, "level1Folder", false)).thenReturn(
                level1FolderEntity,
            )
            whenever(classroomFileRepository.save(level1FolderEntity.copy(state = ClassroomFileState.PUBLISHED, publishedAt = nowTime)))
                .thenReturn(level1FolderEntity.copy(state = ClassroomFileState.PUBLISHED, publishedAt = nowTime))

            classroomFileServiceImpl.updateClassroomFileState(classroomId, "level2File", ClassroomFileState.PUBLISHED)
            verify(classroomFileRepository).save(level1FolderEntity.copy(state = ClassroomFileState.PUBLISHED, publishedAt = nowTime))
            verify(classroomFileRepository).save(level2FileEntity.copy(state = ClassroomFileState.PUBLISHED, publishedAt = nowTime))
        }

        @Test
        internal fun `update classroom file state unPublish successfully`() {
            buildPublishedClassroomFileStructure()
            whenever(classroomFileRepository.findByClassroomIdAndIdAndRootFolder(classroomId, "level2File", false)).thenReturn(
                level2FileEntity,
            )
            whenever(classroomFileRepository.findByClassroomIdAndIdAndDeleted(classroomId, "level2File", false)).thenReturn(
                level2FileEntity,
            )
            whenever(classroomFileRepository.save(level2FileEntity.copy(state = ClassroomFileState.UNPUBLISHED, publishedAt = nowTime)))
                .thenReturn(level2FileEntity.copy(state = ClassroomFileState.UNPUBLISHED, publishedAt = nowTime))

            classroomFileServiceImpl.updateClassroomFileState(classroomId, "level2File", ClassroomFileState.UNPUBLISHED)
            verify(classroomFileRepository).save(level2FileEntity.copy(state = ClassroomFileState.UNPUBLISHED, publishedAt = nowTime))
        }

        @Test
        internal fun `throw ClassroomFileNotFoundException when update classroom file state and classroom file is not existed`() {
            whenever(classroomFileRepository.findByClassroomIdAndIdAndRootFolder(classroomId, "level2Folder", false)).thenReturn(
                null,
            )

            assertThrows<ClassroomFileNotFoundException> {
                classroomFileServiceImpl.updateClassroomFileState(classroomId, "level2Folder", ClassroomFileState.UNPUBLISHED)
            }
        }

        @Test
        internal fun `move classroom file with state publish successfully`() {
            whenever(classroomFileRepository.findByClassroomIdAndIdAndDeleted(classroomId, "level2Folder", false)).thenReturn(
                level2FolderEntity,
            )
            level3FileEntity.state = ClassroomFileState.PUBLISHED
            whenever(classroomFileRepository.findByClassroomIdAndIdAndDeleted(classroomId, "level3File", false)).thenReturn(
                level3FileEntity,
            )

            whenever(classroomFileRepository.findByClassroomIdAndIdAndDeleted(classroomId, "level2Folder", false)).thenReturn(
                level2FolderEntity,
            )
            whenever(classroomFileRepository.save(level2FolderEntity.copy(state = ClassroomFileState.PUBLISHED, publishedAt = nowTime)))
                .thenReturn(level2FolderEntity.copy(state = ClassroomFileState.PUBLISHED, publishedAt = nowTime))

            whenever(classroomFileRepository.findByClassroomIdAndIdAndDeleted(classroomId, "level1Folder", false)).thenReturn(
                level1FolderEntity,
            )
            whenever(classroomFileRepository.save(level1FolderEntity.copy(state = ClassroomFileState.PUBLISHED, publishedAt = nowTime)))
                .thenReturn(level1FolderEntity.copy(state = ClassroomFileState.PUBLISHED, publishedAt = nowTime))

            classroomFileServiceImpl.moveClassroomFile(classroomId, "level3File", "level2Folder")
            verify(classroomFileRepository).save(level2FolderEntity.copy(state = ClassroomFileState.PUBLISHED, publishedAt = nowTime))
            verify(classroomFileRepository).save(level1FolderEntity.copy(state = ClassroomFileState.PUBLISHED, publishedAt = nowTime))
            verify(classroomFileRepository).save(level3FileEntity.copy(parentId = "level2Folder"))
        }

        @Test
        internal fun `move classroom file with state unPublish successfully`() {
            whenever(classroomFileRepository.findByClassroomIdAndIdAndDeleted(classroomId, "level2Folder", false)).thenReturn(
                level2FolderEntity,
            )
            whenever(classroomFileRepository.findByClassroomIdAndIdAndDeleted(classroomId, "level3File", false)).thenReturn(
                level3FileEntity,
            )

            classroomFileServiceImpl.moveClassroomFile(classroomId, "level3File", "level2Folder")
            verify(classroomFileRepository).save(level3FileEntity.copy(parentId = "level2Folder"))
        }

        @Test
        internal fun `throw invalid classroom fail exception when move classroom file with target type file`() {
            whenever(classroomFileRepository.findByClassroomIdAndIdAndDeleted(classroomId, "level3Folder", false)).thenReturn(
                level3FolderEntity,
            )
            whenever(classroomFileRepository.findByClassroomIdAndIdAndDeleted(classroomId, "level2Folder", false)).thenReturn(
                level2FolderEntity,
            )
            assertThrows<InvalidClassroomFileException> {
                classroomFileServiceImpl.moveClassroomFile(classroomId, "level2Folder", "level3Folder")
            }
        }

        @Test
        internal fun `throw exception when move classroom file with to be moved type folder`() {
            whenever(classroomFileRepository.findByClassroomIdAndIdAndDeleted(classroomId, "level2File", false)).thenReturn(
                level2FileEntity,
            )
            assertThrows<InvalidClassroomFolderException> {
                classroomFileServiceImpl.moveClassroomFile(classroomId, "level2Folder", "level2File")
            }
        }

        private fun buildPublishedClassroomFileStructure() {
            level1FolderEntity.state = ClassroomFileState.PUBLISHED
            level2FolderEntity.state = ClassroomFileState.PUBLISHED
            level3FolderEntity.state = ClassroomFileState.PUBLISHED
            level3FileEntity.state = ClassroomFileState.PUBLISHED
            level4FileEntity.state = ClassroomFileState.PUBLISHED
        }
    }

    @Test
    internal fun `should delete classroom file successfully when classroom file is existed`() {
        whenever(classroomFileRepository.findByClassroomIdAndIdAndRootFolder(classroomId, "root1", false)).thenReturn(
            ClassroomFileEntity(
                id = "root1",
                name = "root1Name",
                parentId = "root",
                type = ClassroomFileType.FOLDER,
                uploadedBy = mockUserId,
                classroomId = classroomId,
                state = ClassroomFileState.PUBLISHED,
                createdAt = LocalDateTime.of(2022, 3, 14, 15, 16, 17),
            ),
        )
        classroomFileServiceImpl.deleteClassroomFile(classroomId, "root1")
        verify(classroomFileRepository, times(1)).save(classroomFileCapture.capture())
        assertEquals(classroomFileCapture.value.deleted, true)
    }

    @Test
    internal fun `should do nothing when delete classroom file and classroom file is not existed`() {
        whenever(classroomFileRepository.findByClassroomIdAndIdAndRootFolder(classroomId, "root1", false)).thenReturn(
            null,
        )
        classroomFileServiceImpl.deleteClassroomFile(classroomId, "root1")
        verify(classroomFileRepository, times(0)).save(any())
    }

    @Test
    internal fun `create classroom file successfully when type file`() {
        val s3FileEntity =
            S3FileEntity(
                id = "fileId",
                extension = "jpg",
                bucket = "bucket",
                objectKey = "objectkey",
            )

        val classroomFileEntity =
            ClassroomFileEntity(
                name = "file_test",
                parentId = "parentId",
                s3FileEntity = s3FileEntity,
                type = ClassroomFileType.FILE,
                uploadedBy = mockUserId,
                rootFolder = false,
                deleted = false,
                classroomId = classroomId,
                state = ClassroomFileState.UNPUBLISHED,
            )

        val classroomFile =
            ClassroomFile(
                name = "file_test.jpg",
                parentId = "parentId",
                s3File = S3File.from(s3FileEntity),
                type = ClassroomFileType.FILE,
                uploadedBy = mockUserId,
                rootFolder = false,
                deleted = false,
                classroomId = classroomId,
                state = ClassroomFileState.UNPUBLISHED,
            )

        whenever(s3FileRepository.findById("fileId")).thenReturn(Optional.of(s3FileEntity))
        whenever(classroomFileRepository.save(classroomFileEntity)).thenReturn(classroomFileEntity)

        val expectedClassroomFile = classroomFileServiceImpl.createClassroomFileOrFolder(classroomFile, "fileId")
        assertEquals(expectedClassroomFile.s3File!!.id, "fileId")
        assertEquals(expectedClassroomFile.name, "file_test")
        assertEquals(expectedClassroomFile.parentId, "parentId")
        assertEquals(expectedClassroomFile.state, ClassroomFileState.UNPUBLISHED)
        assertEquals(expectedClassroomFile.type, ClassroomFileType.FILE)
        assertEquals(expectedClassroomFile.uploadedBy, "TeacherName")
    }

    @Test
    internal fun `create classroom file successfully when type folder`() {
        val classroomFileEntity =
            ClassroomFileEntity(
                name = "name",
                parentId = "parentId",
                type = ClassroomFileType.FOLDER,
                uploadedBy = mockUserId,
                rootFolder = false,
                deleted = false,
                classroomId = classroomId,
                state = ClassroomFileState.UNPUBLISHED,
            )

        val classroomFile =
            ClassroomFile(
                name = "name",
                parentId = "parentId",
                type = ClassroomFileType.FOLDER,
                uploadedBy = mockUserId,
                rootFolder = false,
                deleted = false,
                classroomId = classroomId,
                state = ClassroomFileState.UNPUBLISHED,
            )

        whenever(classroomFileRepository.save(classroomFileEntity)).thenReturn(classroomFileEntity.copy(id = "id"))

        val expectedClassroomFile = classroomFileServiceImpl.createClassroomFileOrFolder(classroomFile, null)
        assertEquals(expectedClassroomFile.id, "id")
        assertEquals(expectedClassroomFile.name, "name")
        assertEquals(expectedClassroomFile.parentId, "parentId")
        assertEquals(expectedClassroomFile.state, ClassroomFileState.UNPUBLISHED)
        assertEquals(expectedClassroomFile.type, ClassroomFileType.FOLDER)
        assertEquals(expectedClassroomFile.uploadedBy, "TeacherName")
    }

    @Test
    internal fun `upload file successfully`() {
        val file =
            MockMultipartFile(
                "file",
                "fileName.jpeg",
                MediaType.APPLICATION_JSON_VALUE,
                "content".toByteArray(),
            )
        whenever(s3FileRepository.save(any<S3FileEntity>())).thenReturn(
            S3FileEntity(
                "id",
                "jpg",
                "test-marsladder-classroom-files",
                "key",
            ),
        )
        whenever(objectClient.putObject(any<String>(), any<String>(), any<MockMultipartFile>(), any<String>())).thenReturn(null)

        classroomFileServiceImpl.uploadClassroomFile(classroomId, file)
        verify(s3FileRepository).save(s3FileEntityCaptor.capture())
        assertEquals(s3FileEntityCaptor.value.bucket, "test-marsladder-classroom-files")
        assertEquals(s3FileEntityCaptor.value.extension, "jpeg")
    }

    @Test
    internal fun `initialize classroom root folder successfully`() {
        val mockCourseEntity =
            CourseEntity(
                id = "courseId",
                code = "code2",
                name = "English",
                subjectId = "mock subject id",
                grade = 2,
                icon = "icon",
            )

        whenever(courseRepository.findById("courseId")).thenReturn(Optional.of(mockCourseEntity))

        val entity =
            ClassroomFileEntity(
                name = "Y2 English Files",
                type = ClassroomFileType.FOLDER,
                state = ClassroomFileState.PUBLISHED,
                rootFolder = true,
                classroomId = classroomId,
            )
        whenever(classroomFileRepository.save(entity)).thenReturn(entity.copy(id = "id"))
        val expectedClassroomFile = classroomFileServiceImpl.initializeClassroomRootFolder(classroomId)
        assertEquals(expectedClassroomFile.id, "id")
        assertEquals(expectedClassroomFile.name, "Y2 English Files")
        assertEquals(expectedClassroomFile.parentId, null)
        assertEquals(expectedClassroomFile.rootFolder, true)
        assertEquals(expectedClassroomFile.state, ClassroomFileState.PUBLISHED)
        assertEquals(expectedClassroomFile.type, ClassroomFileType.FOLDER)
        assertEquals(expectedClassroomFile.uploadedBy, null)
    }

    @Test
    internal fun `should return folder structure successfully`() {
        val folderId = "folderId"
        val classroomFileEntity =
            ClassroomFileEntity(
                id = folderId,
                name = "Root Folder",
                type = ClassroomFileType.FOLDER,
                classroomId = classroomId,
                state = ClassroomFileState.PUBLISHED,
                deleted = false,
            )

        val childFolderEntity =
            ClassroomFileEntity(
                id = "childFolderId",
                name = "Child Folder",
                type = ClassroomFileType.FOLDER,
                parentId = folderId,
                classroomId = classroomId,
                state = ClassroomFileState.PUBLISHED,
                deleted = false,
            )

        whenever(classroomFileRepository.findByClassroomIdAndRootFolder(classroomId, true)).thenReturn(listOf(classroomFileEntity))
        whenever(classroomFileRepository.findByParentIdAndTypeAndDeleted(folderId, ClassroomFileType.FOLDER, false))
            .thenReturn(listOf(childFolderEntity))

        val folderStructure = classroomFileServiceImpl.getFolderStructure(classroomId, null)

        assertEquals(folderId, folderStructure.id)
        assertEquals("Root Folder", folderStructure.name)
        assertEquals(1, folderStructure.childrenFolders.size)
        assertEquals("childFolderId", folderStructure.childrenFolders[0].id)
        assertEquals("Child Folder", folderStructure.childrenFolders[0].name)
    }

    @Test
    internal fun `should throw ClassroomFileNotFoundException when root folder is not found`() {
        whenever(classroomFileRepository.findByClassroomIdAndRootFolder(classroomId, true)).thenReturn(emptyList())

        assertThrows<ClassroomFileNotFoundException> {
            classroomFileServiceImpl.getFolderStructure(classroomId, null)
        }
    }

    @Test
    internal fun `get classroom file by and file id successfully`() {
        val classroomFileEntity =
            ClassroomFileEntity(
                id = "id",
                name = "fileName",
                type = ClassroomFileType.FILE,
                s3FileEntity =
                    S3FileEntity(
                        id = "file_id",
                        extension = "jpg",
                        bucket = "bucket",
                        objectKey = "key",
                        createdAt = LocalDateTime.of(2022, 3, 14, 15, 16, 17),
                    ),
                parentId = "rootId",
                uploadedBy = mockUserId,
                classroomId = classroomId,
                state = ClassroomFileState.PUBLISHED,
                createdAt = LocalDateTime.of(2022, 3, 14, 15, 16, 17),
            )

        whenever(classroomFileRepository.findByIdAndDeleted("id", false)).thenReturn(classroomFileEntity)

        val expectedClassroomFile = classroomFileServiceImpl.getClassroomFileDetails("id")
        assertEquals(expectedClassroomFile.id, "id")
        assertEquals(expectedClassroomFile.s3File!!.id, "file_id")
        assertEquals(expectedClassroomFile.name, "fileName")
        assertEquals(expectedClassroomFile.parentId, "rootId")
        assertEquals(expectedClassroomFile.state, ClassroomFileState.PUBLISHED)
        assertEquals(expectedClassroomFile.type, ClassroomFileType.FILE)
        assertEquals(expectedClassroomFile.deleted, false)
        assertEquals(expectedClassroomFile.uploadedBy, "TeacherName")
        assertEquals(expectedClassroomFile.rootFolder, false)
    }
}
