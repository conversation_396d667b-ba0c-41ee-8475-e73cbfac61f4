spring:
  security:
    oauth2:
      resourceserver:
        jwt:
          jwk-set-uri: https://cognito-idp.ap-southeast-2.amazonaws.com/ap-southeast-2_5XijcEWvj/.well-known/jwks.json
  flyway:
    enabled: false
  datasource:
    driverClassName: org.h2.Driver
    url: jdbc:h2:mem:development;MODE=MYSQL
    username: sa
    password: 123456
  h2:
    console.enabled: true
  jpa:
    hibernate:
      ddl-auto: create
    show-sql: true
  kafka:
    bootstrap-servers: localhost:9092
    properties:
      security:
        protocol: PLAINTEXT

practice-created-queue:
  url: https://sqs.ap-southeast-2.amazonaws.com/862276445914/practice-created-queue

practice-robot-queue:
  url: https://sqs.ap-southeast-2.amazonaws.com/862276445914/practice-robot-queue

marketing-events-queue:
  url: https://sqs.ap-southeast-2.amazonaws.com/862276445914/marketing-events-queue

cognito:
  user-pool-id: ap-southeast-2_5XijcEWvj

logging:
  level:
    org:
      drools: DEBUG

subscription:
  stripe:
    secret: sk_test_51JyBgyG5mahiNahYNgpjzNrlerFa6NOtU0972wjmRKoEthSpH2H82LfG1hGtukVZVk0KjJP9W1KGq2nfT0tExIgb00TRIheDDs
    product: prod_KdSvNVwbeYPPsj
    seat-product: prod_N0w7APrctvlQzh
    teacher-product: prod_SMUNxrv232EPbK
    tax-rate: txr_1JyBzoG5mahiNahYxhm7eDtK
    webhook-secret: local-webhook-secret

nvwa:
  host: http://nvwa

klaviyo:
  enabled: false
  host: https://a.klaviyo.com
  private-api-key: xxx
  marsladder-users-all-list: xxx

facebook:
  app-secret: 123abc

fastcomments:
  tenant-id: "123"
  api-key: "123"
  api-base-url: https://fastcomments.com/api/v1

logging.level.org.hibernate.SQL: DEBUG
logging.level.org.hibernate.type.descriptor.sql.BasicBinder: TRACE
