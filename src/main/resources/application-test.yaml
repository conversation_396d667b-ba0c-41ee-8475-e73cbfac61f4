springdoc:
  api-docs:
    enabled: false
  swagger-ui:
    enabled: false

spring:
  security:
    oauth2:
      resourceserver:
        jwt:
          jwk-set-uri: https://cognito-idp.ap-southeast-2.amazonaws.com/ap-southeast-2_5XijcEWvj/.well-known/jwks.json
  datasource:
    url: ******************************************************
    username: ladygaga
  kafka:
    bootstrap-servers: 'b1.test.marsladder.com.au:9092,b2.test.marsladder.com.au:9092,b3.test.marsladder.com.au:9092'
    properties:
      security:
        protocol: PLAINTEXT

practice-created-queue:
  url: https://sqs.ap-southeast-2.amazonaws.com/862276445914/practice-created-queue

practice-robot-queue:
  url: https://sqs.ap-southeast-2.amazonaws.com/862276445914/practice-robot-queue

marketing-events-queue:
  url: https://sqs.ap-southeast-2.amazonaws.com/862276445914/marketing-events-queue

cognito:
  user-pool-id: ap-southeast-2_5XijcEWvj

activemq:
  endpoint: mq.test.marsladder.com.au

redis:
  url:
    host: redis.test.marsladder.com.au
    port: 6379

subscription:
  stripe:
    product: prod_KiS1ABYGVNeBUT
    seat-product: prod_N0w7APrctvlQzh
    teacher-product: prod_SMUNxrv232EPbK
    tax-rate: txr_1K317ILwqo2awcASjERszGIE
  bundle:
    trial-course-enabled: true

nvwa:
  host: ${NVWA_HOST}

klaviyo:
  enabled: true
  host: ${KLAVIYO_HOST}
  private-api-key: ${KLAVIYO_PRIVATE_API_KEY}
  marsladder-users-all-list: ${KLAVIYO_MARSLADDER_USERS_ALL_LIST}

facebook:
  deletion-status-url: https://test.marsladder.com.au/facebook-user-data-deletion-requests/

client:
  xiaogj:
    host: ${XIAOGJ_HOST}

