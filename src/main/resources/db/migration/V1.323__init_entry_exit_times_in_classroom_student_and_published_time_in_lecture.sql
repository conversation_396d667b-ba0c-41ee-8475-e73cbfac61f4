UPDATE classroom_student
SET entry_exit_times = CASE
    WHEN status = 'IN' THEN JSON_ARRAY(DATE_FORMAT(created_at, '%Y-%m-%d %H:%i:%s'))
    WHEN status = 'OUT' THEN JSON_ARRAY(DATE_FORMAT(created_at, '%Y-%m-%d %H:%i:%s'), DATE_FORMAT(updated_at, '%Y-%m-%d %H:%i:%s'))
    ELSE entry_exit_times
END;

UPDATE classroom_lecture
SET published_at = created_at
WHERE state = 'PUBLISHED' AND published_at IS NULL;

UPDATE classroom_file
SET published_at = created_at
WHERE state = 'PUBLISHED' AND published_at IS NULL;