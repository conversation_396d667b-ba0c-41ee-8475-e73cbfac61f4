ALTER TABLE realus_profile
    MODIFY COLUMN xiaogj_student_id VARCHAR(36) NOT NULL AFTER id;

ALTER TABLE realus_profile
    ADD COLUMN xiaogj_name VARCHAR(36) DEFAULT '' NOT NULL
    AFTER xiaogj_account;

ALTER TABLE realus_profile
    ADD COLUMN xiaogj_serial VARCHAR(36) DEFAULT '' NOT NULL
    AFTER xiaogj_name;

CREATE INDEX idx_xiaogj_student_id ON realus_profile(xiaogj_student_id);
CREATE INDEX idx_xiaogj_name ON realus_profile(xiaogj_name);
CREATE INDEX idx_xiaogj_serial ON realus_profile(xiaogj_serial);
