DROP PROCEDURE if exists calculate_practice_statistics;

DELIMITER ;;
CREATE PROCEDURE calculate_practice_statistics()
BEGIN
    DECLARE v_practice_id VARCHAR(36);
    DECLARE v_cognito_uid VARCHAR(36);
    DECLARE v_player_type VARCHAR(36);
    DECLARE v_course_id VARCHAR(36);
    DECLARE v_mode VARCHAR(50);
    DECLARE v_players JSON;
    DECLARE v_player JSON;
    DECLARE v_total_seconds BIGINT;
    DECLARE v_total_answer INT;
    DECLARE v_total_correct_answer INT;
    DECLARE v_start_at DATETIME;
    DECLARE v_end_at DATETIME;

    DECLARE finished INT DEFAULT 0;
    DECLARE loop_index INT DEFAULT 0;
    DECLARE practice_cursor CURSOR FOR SELECT id, course_id, mode, start_at, end_at, players
                                       FROM practice
                                       WHERE status = 'FINISHED'
                                         AND mode NOT IN ('CASUAL_TASK', 'CHALLENGE_TASK');
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET finished = 1;

    OPEN practice_cursor;
    practice_loop:
    LOOP
        FETCH practice_cursor INTO v_practice_id, v_course_id, v_mode, v_start_at, v_end_at, v_players;

        IF (finished) THEN
            LEAVE practice_loop;
        END IF;

        SET loop_index = 0;

        WHILE loop_index < JSON_LENGTH(v_players)
            DO
                SELECT JSON_EXTRACT(v_players, CONCAT('$[', loop_index, ']')) INTO v_player;

                SET v_cognito_uid = JSON_UNQUOTE(JSON_EXTRACT(v_player, '$.cognitoUid'));
                SET v_player_type = JSON_UNQUOTE(JSON_EXTRACT(v_player, '$.playerType'));

                IF (v_player_type != 'ROBOT') THEN
                    SET v_total_seconds = TIMESTAMPDIFF(SECOND, v_start_at, v_end_at);
                    IF (v_total_seconds IS NULL OR v_total_seconds < 0) THEN
                        SET v_total_seconds = 0;
                    END IF;

                    SET v_total_answer = JSON_LENGTH(v_player, '$.answers');
                    IF (v_total_answer IS NULL) THEN
                        SET v_total_answer = 0;
                    END IF;

                    SELECT COUNT(*)
                    INTO v_total_correct_answer
                    FROM JSON_TABLE(v_player, '$.answers[*]' COLUMNS (is_correct BOOLEAN PATH '$.correct')) AS answers
                    WHERE answers.is_correct = true;
                    IF (v_total_correct_answer IS NULL) THEN
                        SET v_total_correct_answer = 0;
                    END IF;

                    IF (v_mode = 'CASUAL' AND v_total_seconds > 7200) THEN
                        SET v_total_seconds = 0;
                        SET v_total_answer = 0;
                        SET v_total_correct_answer = 0;
                    END IF;

                    IF NOT EXISTS(SELECT 1
                                  FROM practice_statistics
                                  WHERE cognito_uid = v_cognito_uid
                                    AND course_id = v_course_id
                                    AND mode = v_mode) THEN
                        INSERT INTO practice_statistics (id, cognito_uid, course_id, mode, total_seconds, total_answer,
                                                         total_correct_answer)
                        VALUES (UUID(), v_cognito_uid, v_course_id, v_mode, v_total_seconds, v_total_answer,
                                v_total_correct_answer);
                    ELSE
                        UPDATE practice_statistics
                        SET total_seconds        = total_seconds + v_total_seconds,
                            total_answer         = total_answer + v_total_answer,
                            total_correct_answer = total_correct_answer + v_total_correct_answer
                        WHERE cognito_uid = v_cognito_uid
                          AND course_id = v_course_id
                          AND mode = v_mode;
                    END IF;
                END IF;

                SELECT loop_index + 1 INTO loop_index;

            END WHILE;
    END LOOP;

    CLOSE practice_cursor;
END ;;
DELIMITER ;