DROP PROCEDURE if exists update_question_contents_and_sub_topics_procedure;

DELIMITER ;;
CREATE PROCEDURE `update_question_contents_and_sub_topics_procedure`(v_question_id char(36))
begin
declare v_contents json default json_object();
declare v_content json default json_object();
declare v_sub_topics json default json_object();
declare v_sub_topic json default json_object();

DECLARE cursor_content CURSOR FOR select json_object(b.course_outline_id, JSON_ARRAYAGG(a.content_id)) as contents from (
select * from question_content
where question_id = v_question_id ) a
left join course_outline_item b
on a.content_id = b.id
group by b.course_outline_id;

DECLARE cursor_sub_topic CURSOR FOR select json_object(course_outline_id, JSON_ARRAYAGG(id)) as sub_topics from (
select b.course_outline_id, c.id from (
select * from question_content
where question_id = v_question_id ) a
left join course_outline_item b
on a.content_id = b.id
left join course_outline_item c
on b.parent_id = c.id
group by b.course_outline_id, c.id ) d
group by course_outline_id;


OPEN cursor_content;
BEGIN
	DECLARE done INT DEFAULT 0;
	DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = 1;
	read_loop: LOOP
		FETCH cursor_content INTO v_content;
		IF done = 1 THEN
       		LEAVE read_loop;
    	END IF;
    	select json_merge(v_contents, v_content) into v_contents;
	END LOOP;
END;
CLOSE cursor_content;

OPEN cursor_sub_topic;
BEGIN
	DECLARE done INT DEFAULT 0;
	DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = 1;
	read_loop: LOOP
		FETCH cursor_sub_topic INTO v_sub_topic;
		IF done = 1 THEN
       		LEAVE read_loop;
    	END IF;
    	select json_merge(v_sub_topics, v_sub_topic) into v_sub_topics;
	END LOOP;
END;
CLOSE cursor_sub_topic;

update question_additional_attributes
set contents = v_contents
where question_id = v_question_id
;

update question_additional_attributes
set sub_topics = v_sub_topics
where question_id = v_question_id
;

end;;
DELIMITER ;
