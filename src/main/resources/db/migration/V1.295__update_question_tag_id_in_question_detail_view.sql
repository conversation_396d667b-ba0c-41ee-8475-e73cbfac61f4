CREATE
OR REPLACE VIEW view_question_detail AS
SELECT q.*,
       qaa.answered_count,
       qaa.correct_count,
       qaa.answered_student_count,
       qaa.answered_students,
       qt.id       AS question_tag_id,
       qt.tag_name AS question_tag
FROM question q
         LEFT JOIN question_additional_attributes qaa
                   ON q.id = qaa.question_id
         LEFT JOIN question_question_tag qqt
                   ON q.id = qqt.question_id
         LEFT JOIN question_tag qt
                   ON qqt.question_tag_id = qt.id
;
