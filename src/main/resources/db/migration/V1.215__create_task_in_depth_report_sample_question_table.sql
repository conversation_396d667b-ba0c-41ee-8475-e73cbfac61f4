CREATE TABLE `task_in_depth_report_sample_question`
(
    `id`                   char(36)    NOT NULL,
    `task_id`              varchar(36) NOT NULL,
    `question`             JSON NOT NULL,
    `created_at`           timestamp   NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at`           timestamp   NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    <PERSON>EY `task_in_depth_report_sample_question_task_id_index` (`task_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8;
