DROP PROCEDURE if exists update_question_additional_attributes_by_practice;

DELIMITER ;;
CREATE PROCEDURE `update_question_additional_attributes_by_practice`()
begin
declare v_practice_players json;
declare v_answers json;
declare v_answer json;
declare v_practice_id char(36);
declare v_student_cognito_uid char(36);
declare v_player_type enum('STUDENT','ROBOT', 'TEACHER', 'TUTOR');
declare v_question_id char(36);
declare v_original_question_id char(36);
declare v_correct tinyint(1);

DECLARE v_i INT DEFAULT 0;
DECLARE v_j INT DEFAULT 0;


DECLARE done INT DEFAULT 0;
DECLARE cursor_i CURSOR FOR SELECT ID FROM practice;
DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = 1;


update question_additional_attributes
set answered_count = 0,
correct_count = 0,
answered_student_count = 0,
answered_students = JSON_ARRAY();

OPEN cursor_i;
read_loop: LOOP
	FETCH cursor_i INTO v_practice_id;
	IF done = 1 THEN
       LEAVE read_loop;
    END IF;

	select players into v_practice_players from practice where id = v_practice_id;

	SELECT 0 INTO v_i;
    WHILE v_i < JSON_LENGTH(v_practice_players) DO
        SELECT JSON_EXTRACT(v_practice_players,CONCAT('$[',v_i,'].answers')) INTO v_answers;
        SELECT json_unquote(JSON_EXTRACT(v_practice_players,CONCAT('$[',v_i,'].cognitoUid'))) INTO v_student_cognito_uid;
        SELECT json_unquote(JSON_EXTRACT(v_practice_players,CONCAT('$[',v_i,'].playerType'))) INTO v_player_type;

        if v_player_type = 'STUDENT' THEN
        	SELECT 0 INTO v_j;
        	WHILE v_j < JSON_LENGTH(v_answers) DO
        		SELECT JSON_EXTRACT(v_answers,CONCAT('$[',v_j,']')) INTO v_answer;
        		SELECT v_answer->'$.correct' into v_correct;
        		SELECT json_unquote(v_answer->'$.questionId') into v_question_id;
        		select json_unquote(JSON_EXTRACT(questions, concat(json_unquote(json_search(questions->'$[*].id', 'one', v_question_id)), '.originalQuestionId'))) from practice where id = v_practice_id into v_original_question_id;

        		update question_additional_attributes q
        		set answered_count = answered_count + 1,
        		correct_count = case when v_correct then correct_count + 1 else correct_count end
        		where question_id = v_original_question_id;

        		update question_additional_attributes q
        		set answered_students = json_merge(json_quote(v_student_cognito_uid), answered_students)
        		where question_id = v_original_question_id
        		and not JSON_CONTAINS(answered_students, json_quote(v_student_cognito_uid));

        		SELECT v_j + 1 INTO v_j;
        	END WHILE;
        END IF;
        SELECT v_i + 1 INTO v_i;
    END WHILE;
END LOOP;
CLOSE cursor_i;

update question_additional_attributes
set answered_student_count = JSON_LENGTH(answered_students);

end;;
DELIMITER ;
