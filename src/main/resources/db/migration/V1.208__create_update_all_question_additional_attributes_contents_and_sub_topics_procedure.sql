DROP PROCEDURE if exists update_all_question_contents_and_sub_topics_procedure;

DE<PERSON>IMITER ;;
CREATE PROCEDURE `update_all_question_contents_and_sub_topics_procedure`()
begin
declare v_question_id char(36);

DECLARE cursor_i CURSOR FOR select question_id from question_additional_attributes;

OPEN cursor_i;
BEGIN
	DECLARE done INT DEFAULT 0;
	DECLARE CONTINUE HANDLER FOR NOT FOUND SET done = 1;
	read_loop: LOOP
		FETCH cursor_i INTO v_question_id;
		IF done = 1 THEN
       		LEAVE read_loop;
    	END IF;
    	call update_question_contents_and_sub_topics_procedure(v_question_id);
	END LOOP;
END;
CLOSE cursor_i;

end;;
DELIMITER ;
