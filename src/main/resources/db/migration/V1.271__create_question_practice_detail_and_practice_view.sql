CREATE VIEW view_question_practice_detail_and_practice AS
SELECT de.question_id,
       de.correct,
       de.course_id,
       de.spent_seconds,
       de.question_num,
       de.avg_spent_seconds,
       p.status,
       p.mode,
       p.start_at,
       de.student_id,
       ROW_NUMBER() OVER (PARTITION BY de.question_id ORDER BY p.start_at DESC) AS answer_rank
FROM question_practice_detail as de
LEFT JOIN practice p ON p.id = de.practice_id;



