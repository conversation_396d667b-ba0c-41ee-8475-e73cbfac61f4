ALTER TABLE student_rank
    ADD `total_time` BIGINT default 0 AFTER `star`;

ALTER TABLE student_rank
    ADD `total_answer` INT default 0 AFTER `star`;

ALTER TABLE student_rank
    ADD `total_correct_answer` INT default 0 AFTER `star`;

ALTER table student_rank
    ADD INDEX course_id_rank_level_star_index(course_id, rank_level, star);

ALTER table student_rank
    ADD INDEX course_id_cognito_uid_rank_level_star_index(course_id, cognito_uid, rank_level, star);
