DROP PROCEDURE if exists calculate_student_rank_of_course_outline;

DE<PERSON><PERSON>ITER ;;
CREATE PROCEDURE calculate_student_rank_of_course_outline()

BEGIN
    DECLARE v_cognito_uid VARCHAR(36);
    DECLARE v_course_id VARCHAR(36);
    DECLARE v_course_outline_id VARCHAR(36);
    DECLARE v_area_of_study JSON;
    DECLARE v_rank_level INT;
    DECLARE v_refreshed_rank_level INT;
    DECLARE v_star INT;
    DECLARE v_star_sum INT;
    DECLARE v_refreshed_star INT;
    DECLARE v_total_correct_answer INT;
    DECLARE v_total_correct_answer_sum INT;
    DECLARE v_total_answer INT;
    DECLARE v_total_answer_sum INT;
    DECLARE v_total_time BIGINT;
    DECLARE v_total_time_sum BIGINT;

    DECLARE finished INT DEFAULT 0;
    DECLARE loop_index INT DEFAULT 0;
    DECLARE area_rank_cursor CURSOR FOR
        SELECT aos.cognito_uid,
               aos.course_id,
               coi.course_outline_id,
               JSON_ARRAYAGG(JSON_OBJECT('rank_level', aos.rank_level, 'star', aos.star, 'total_answer',
                                         aos.total_answer, 'total_correct_answer', aos.total_correct_answer,
                                         'total_time', aos.total_time))
        FROM area_of_study_rank aos
                 LEFT JOIN course_outline_item coi on aos.area_of_study_id = coi.id
        WHERE coi.id is not null
        GROUP BY coi.course_outline_id, aos.cognito_uid, aos.course_id;
    DECLARE CONTINUE HANDLER FOR NOT FOUND SET finished = 1;

    OPEN area_rank_cursor;
    rank_loop:
    LOOP
        FETCH area_rank_cursor INTO v_cognito_uid, v_course_id, v_course_outline_id, v_area_of_study;

        IF (finished) THEN
            LEAVE rank_loop;
        END IF;

        SET loop_index = 0;

        SET v_star_sum = 0;
        SET v_total_correct_answer_sum = 0;
        SET v_total_answer_sum = 0;
        SET v_total_time_sum = 0;

        WHILE loop_index < JSON_LENGTH(v_area_of_study)
            DO
                SELECT JSON_UNQUOTE(JSON_EXTRACT(v_area_of_study, CONCAT('$[', loop_index, '].rank_level')))
                INTO v_rank_level;
                SELECT JSON_UNQUOTE(JSON_EXTRACT(v_area_of_study, CONCAT('$[', loop_index, '].star'))) INTO v_star;
                SELECT JSON_UNQUOTE(JSON_EXTRACT(v_area_of_study, CONCAT('$[', loop_index, '].total_correct_answer')))
                INTO v_total_correct_answer;
                SELECT JSON_UNQUOTE(JSON_EXTRACT(v_area_of_study, CONCAT('$[', loop_index, '].total_answer')))
                INTO v_total_answer;
                SELECT JSON_UNQUOTE(JSON_EXTRACT(v_area_of_study, CONCAT('$[', loop_index, '].total_time')))
                INTO v_total_time;

                IF (v_rank_level = 0) THEN
                    SELECT v_star + v_star_sum INTO v_star_sum;
                ELSE
                    SELECT COALESCE(SUM(star), 0) + v_star + v_star_sum
                    FROM rank_information
                    WHERE rank_level < v_rank_level
                    INTO v_star_sum;
                END IF;

                SELECT v_total_correct_answer + v_total_correct_answer_sum INTO v_total_correct_answer_sum;
                SELECT v_total_answer + v_total_answer_sum INTO v_total_answer_sum;
                SELECT v_total_time + v_total_time_sum INTO v_total_time_sum;

                SELECT loop_index + 1 INTO loop_index;
            END WHILE;

        SET v_refreshed_rank_level = 0;
        SET v_refreshed_star = 0;

        IF (JSON_LENGTH(v_area_of_study) > 0) THEN
            SET v_star_sum = v_star_sum DIV JSON_LENGTH(v_area_of_study);

            IF (v_star_sum > 2) THEN
                SELECT ri1.rank_level, v_star_sum - SUM(ri2.star)
                INTO v_refreshed_rank_level, v_refreshed_star
                FROM rank_information AS ri1
                         JOIN rank_information AS ri2 ON ri1.rank_level > ri2.rank_level
                GROUP BY ri1.rank_level
                HAVING SUM(ri2.star) < v_star_sum
                ORDER BY ri1.rank_level DESC
                LIMIT 1;
            ELSE
                SELECT 0, v_star_sum INTO v_refreshed_rank_level, v_refreshed_star;
            END IF;

            IF NOT EXISTS(SELECT 1
                          FROM student_rank
                          WHERE cognito_uid = v_cognito_uid
                            AND course_id = v_course_id
                            AND course_outline_id = v_course_outline_id) THEN
                INSERT INTO student_rank (id, cognito_uid, course_id, course_outline_id, rank_level, star,
                                          total_correct_answer, total_answer, total_time)
                VALUES (UUID(), v_cognito_uid, v_course_id, v_course_outline_id, v_refreshed_rank_level,
                        v_refreshed_star, v_total_correct_answer_sum, v_total_answer_sum, v_total_time_sum);
            ELSE
                UPDATE student_rank
                SET rank_level           = v_refreshed_rank_level,
                    star                 = v_refreshed_star,
                    total_time           = v_total_time_sum,
                    total_answer         = v_total_answer_sum,
                    total_correct_answer = v_total_correct_answer_sum
                WHERE cognito_uid = v_cognito_uid
                  AND course_id = v_course_id
                  AND course_outline_id = v_course_outline_id;
            END IF;
        END IF;

    END LOOP;
    CLOSE area_rank_cursor;
END ;;
DELIMITER ;