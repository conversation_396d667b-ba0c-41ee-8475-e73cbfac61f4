CREATE OR REPLACE VIEW student_task_error_review_practice_detail_view AS
SELECT UUID()                    AS id,
       t.id                      AS task_id,
       t.course_id               AS task_course_id,
       t.course_outline_id       AS task_course_outline_id,
       t.area_id                 AS task_area_id,
       t.topics                  AS task_topics,
       t.status                  AS task_status,
       st.id                     AS student_task_id,
       st.student_cognito_uid    AS student_cognito_uid,
       st.status                 AS student_task_status,
       st.practice_count         AS student_task_practice_count,
       stp.practice_id           AS student_task_practice_id,
       p.status                  AS practice_status,
       p.start_at                AS practice_start_at,
       p.end_at                  AS practice_end_at,
       p.players                 AS practice_players,
       pq.original_question_id   AS practice_original_question_id,
       pq.question               AS practice_question,
       pp.id                     AS practice_related_id,
       pp.course_id              AS practice_related_course_id,
       pp.questions              AS practice_related_questions,
       pp.players                AS practice_related_players,
       pp.mode                   AS practice_related_mode,
       pp.start_at               AS practice_related_start_at,
       pp.status                 AS practice_related_status
FROM student_task st
         INNER JOIN task t
                    ON st.task_id = t.id
         LEFT JOIN student_task_practice stp
                   ON st.id = stp.student_task_id
         LEFT JOIN practice p
                   ON stp.practice_id = p.id
         CROSS JOIN JSON_TABLE(
        p.questions, '$[*]'
        COLUMNS (
            original_question_id VARCHAR(36) PATH '$.originalQuestionId',
            question JSON PATH '$'
            )
                    ) AS pq
         LEFT JOIN practice pp
                   ON pp.metadata ->> '$.questionId' = pq.original_question_id
                       AND pp.metadata ->> '$.studentTaskId' = st.id
                       AND pp.mode = 'ERROR_REVIEW';