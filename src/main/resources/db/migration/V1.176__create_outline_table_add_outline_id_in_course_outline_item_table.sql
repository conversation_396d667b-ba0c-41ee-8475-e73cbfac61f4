CREATE TABLE `course_outline`
(
    `id`                 CHAR(36)    NOT NULL,
    `name`               VARCHAR(36) NOT NULL,
    `status` enum('ACTIVE','INACTIVE') NOT NULL DEFAULT 'ACTIVE',
    `created_at`         TIMESTAMP   NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at`         TIMESTAMP   NULL     DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8;

insert into course_outline(id, name, status) values (
uuid(), '2022_outline', 'ACTIVE'
);

insert into course_outline(id, name, status) values (
uuid(), '2023_outline', 'INACTIVE'
);

ALTER TABLE `course_outline_item`
    ADD COLUMN `course_outline_id` CHAR(36) AFTER id;

update course_outline_item set course_outline_id = (select id from course_outline where name = '2022_outline');

