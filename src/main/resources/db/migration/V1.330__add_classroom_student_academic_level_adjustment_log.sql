create table `classroom_student_academic_level_adjustment_log`
(
    `id`                     varchar(36) NOT NULL PRIMARY KEY,
    `classroom_id`           varchar(36) NOT NULL,
    `student_user_id`        varchar(36) NOT NULL,
    `academic_level`         ENUM('Excellent', 'Good', 'Exploring'),
    `suggest_academic_level` ENUM('Excellent', 'Good', 'Exploring'),
    `created_at`             timestamp NULL DEFAULT CURRENT_TIMESTAMP,
    `updated_at`             timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
     INDEX idx_classroom_id (`classroom_id`),
     INDEX idx_student_user_id (`student_user_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8;