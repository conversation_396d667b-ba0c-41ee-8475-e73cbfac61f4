spring:
  security:
    oauth2:
      resourceserver:
        jwt:
          jwk-set-uri: https://cognito-idp.ap-southeast-2.amazonaws.com/ap-southeast-2_5XijcEWvj/.well-known/jwks.json
  flyway:
    locations: classpath:/db/migration,/db/testdata
  datasource:
    url: **************************************
    username: root
    password: 123456
  jpa:
    show-sql: true
  kafka:
    bootstrap-servers: localhost:9092
    properties:
      security:
        protocol: PLAINTEXT

practice-created-queue:
  url: https://sqs.ap-southeast-2.amazonaws.com/862276445914/practice-created-queue

practice-robot-queue:
  url: https://sqs.ap-southeast-2.amazonaws.com/862276445914/practice-robot-queue

marketing-events-queue:
  url: https://sqs.ap-southeast-2.amazonaws.com/862276445914/marketing-events-queue

cognito:
  user-pool-id: ap-southeast-2_5XijcEWvj

subscription:
  stripe:
    secret: sk_test_51JyBgyG5mahiNahYNgpjzNrlerFa6NOtU0972wjmRKoEthSpH2H82LfG1hGtukVZVk0KjJP9W1KGq2nfT0tExIgb00TRIheDDs
    product: prod_KdSvNVwbeYPPsj
    seat-product: prod_N0w7APrctvlQzh
    teacher-product: prod_SMUNxrv232EPbK
    tax-rate: txr_1JyBzoG5mahiNahYxhm7eDtK
    webhook-secret: docker-webhook-secret

nvwa:
  host: http://nvwa

klaviyo:
  enabled: false
  host: https://a.klaviyo.com
  private-api-key: xxx
  marsladder-users-all-list: xxx

facebook:
  app-secret: 123abc

fastcomments:
  tenant-id: 123
  api-key: 1234
  api-base-url: https://fastcomments.com/api/v1