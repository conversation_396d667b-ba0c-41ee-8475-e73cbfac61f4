server:
  port: 8080
spring:
  application:
    name: falcon
  profiles:
    active: local
  flyway:
    sql-migration-prefix: V
    repeatable-sql-migration-prefix: R
    sql-migration-separator: __
    sql-migration-suffixes: .sql
  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 100MB
  datasource:
    hikari:
      maximum-pool-size: 30
      max-lifetime: 890000
  data:
    redis:
      repositories:
        enabled: false
  jpa:
    properties:
      hibernate:
        globally_quoted_identifiers: true
        jdbc:
          time_zone: UTC
  kafka:
    consumer:
      enable-auto-commit: false
      auto-offset-reset: latest
      isolation-level: read_committed
      max-poll-records: 1
    properties:
      ssl.endpoint.identification.algorithm: https
      security.protocol: SASL_SSL
      retry.backoff.ms: 500
      sasl:
        mechanism: PLAIN
        jaas:
          enabled: true
          control-flag: required
          config: org.apache.kafka.common.security.plain.PlainLoginModule required username="${spring.kafka.jaas.options.username}" password="${spring.kafka.jaas.options.password}";

s3:
  static:
    bucket: static.test.marsladder.com.au
    path: static
    url: https://static.test.marsladder.com.au
    file:
      size:
        limit: 10000000
  avatar:
    path: avatars
    default: 48.png
  area_icon:
    path: area_icon
    default: Calculus.svg
  course_icon:
    path: course_icon
    default: CourseDefault.svg
  classroom_icon:
    path: assets/class
    default: default.webp
  classroom-file:
    bucket: test-marsladder-classroom-files
    maxFileSize: 52428800
    allowContentType:
      PNG: image/jpeg
      JPG: image/jpeg
      JPEG: image/jpeg
      PDF: application/pdf
      DOC: application/msword
      DOCX: application/vnd.openxmlformats-officedocument.wordprocessingml.document
      PPT: application/vnd.ms-powerpoint
      PPTX: application/vnd.openxmlformats-officedocument.presentationml.presentation
      MP4: video/mp4
      AVI: video/x-msvideo
    presignedUrlExpiration: 6

paypal:
  subscription:
    frequency: monthly

flexmatch:
  matchTypeConfiguration:
    FourPlayersBattle: FourPlayersConfiguration
    ThreePlayersBattle: ThreePlayersConfiguration
    TwoPlayersBattle: TwoPlayersConfiguration
  matchMakingTimeoutSeconds: 3
  battleModeDefaultTeam: battle
  battleTicketMap:
    TwoPlayersBattle: 5
    ThreePlayersBattle: 5
    FourPlayersBattle: 5

practice:
  totalTime:
    ERROR_CHALLENGE: 900
    ERROR_RELATED: 900
    default: 480
  questionCount: 5
  easyQuestionCount: 2
  normalQuestionCount: 2
  hardQuestionCount: 1
  chatMessageTimeout: 3
  trickMessageTimeout: 10
  trickPictureUrl: https://static.test.marsladder.com.au/practice/trick.svg
  freeTrialLimit: 2
  avoidLatestFinishedQuestionCount: 10

amazonoidc:
  region: ap-southeast-2

discourse:
  api:
    key: c985417d514922370e84043ac50f3854a2ca7acf82af1733750677e5497312ed
    baseUrl: https://test-discourse.trydiscourse.com
    defaultUser: system

activemq:
  user: user
  password: password
  port: 61614
  endpoint: localhost

subscription:
  bundle:
    monthly:
      dollarForOne: 19.95
      dollarForTwo: 29.95
    halfYearly:
      dollarForOne: 79.95
      dollarForTwo: 119.95
    yearly:
      dollarForOne: 119.95
      dollarForTwo: 179.95
    trial-course-enabled: false
  seat:
    monthly:
      dollarPerSeat: 6
      minSeats: 3
    halfYearly:
      dollarPerSeat: 5
      minSeats: 3
    yearly:
      dollarPerSeat: 4
      minSeats: 3
  teacher-bundle:
    monthly:
      price: 19.95
      discountPercent: 0
    halfYearly:
      price: 89.95
      discountPercent: 0
    yearly:
      price: 129.95
      discountPercent: 0

offlineTask:
  maxCountPerMonth: 2

aws:
  endpointOverride:

discount:
  referral:
    amountOff: 500
  invitation:
    amountOff: 500
  register:
    amountOff: 1000

nvwa:
  host: ${NVWA_HOST}

klaviyo:
  enabled: false
  host: ${KLAVIYO_HOST}
  private-api-key: ${KLAVIYO_PRIVATE_API_KEY}
  marsladder-users-all-list: ${KLAVIYO_MARSLADDER_USERS_ALL_LIST}

facebook:
  app-secret: ${FACEBOOK_APP_SECRET}
  deletion-status-url: https://host/facebook-user-data-deletion-requests/

fastcomments:
  tenant-id: ${FASTCOMMENTS_TENANT_ID}
  api-key: ${FASTCOMMENTS_API_KEY}
  api-base-url: https://fastcomments.com/api/v1

kafka:
  topic:
    question: question-updates
    classroomCreated: classroom-created
    classroomChanged: classroom-changed
    studentBundleSubscription: student-bundle-subscription
    teacherBundleSubscription: teacher-bundle-subscription
    seatSubscription: seat-subscription
    userTCAccepted: user-tc-accepted
    schoolClassroomStudentChanged: school-classroom-student-changed
    classroomStudentChanged: classroom-student-changed

profile:
  kafka:
    event:
      topic: user-profile
      listener:
        isMonitorEnabled: true
        autoStartup: true
        containerId: userProfileConsumer

marsladder:
  host: test.marsladder.com.au

springdoc:
  api-docs:
    enabled: true
  swagger-ui:
    enabled: true

client:
  nezha:
    url: http://nezha
  diting:
    url: http://diting
  xiaogj:
    url: https://api.xiaogj.com
  fuxi:
    url: http://fuxi
  question-service:
    url: http://question-service
  user-service:
    url: http://user-service

firstedu:
  schoolId: bd95e0d6-973d-446f-8104-97f3b8180bce # schoolName: First Education
  devSchoolId: bd95e0d6-973d-446f-8104-97f3b8180bce # schoolName: First Education
