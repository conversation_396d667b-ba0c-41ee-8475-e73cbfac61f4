package com.firstedu.marsladder.falcon.classroom.controller.dto

import com.firstedu.marsladder.falcon.classroom.ClassroomFileState
import com.firstedu.marsladder.falcon.classroom.ClassroomFileType
import com.firstedu.marsladder.falcon.classroom.service.domain.ClassroomFile
import com.firstedu.marsladder.falcon.utils.XssFilter
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.Size

data class CreateClassroomFileRequest(
    val s3FileId: String? = null,
    @field:Size(max = 255, message = "The maximum length is 255 letters")
    val name: String,
    @field:NotBlank(message = "currentFolderId cannot be null")
    val currentFolderId: String,
    val type: ClassroomFileType,
) {
    fun xssFilter(
        xssFilter: XssFilter,
    ) = CreateClassroomFileRequest(
        s3FileId = s3FileId,
        name = xssFilter.filter(name),
        currentFolderId = currentFolderId,
        type = type,
    )

    fun toClassroomFile(
        classroomId: String,
        uploadedBy: String,
    ) = ClassroomFile(
        name = name,
        parentId = currentFolderId,
        state = ClassroomFileState.UNPUBLISHED,
        type = type,
        deleted = false,
        rootFolder = false,
        uploadedBy = uploadedBy,
        classroomId = classroomId,
    )
}
