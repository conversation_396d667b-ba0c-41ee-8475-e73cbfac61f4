package com.firstedu.marsladder.falcon.classroom.controller.dto

import com.firstedu.marsladder.falcon.classroom.ClassroomFileState
import com.firstedu.marsladder.falcon.classroom.ClassroomFileType
import com.firstedu.marsladder.falcon.classroom.service.domain.ClassroomFile
import com.firstedu.marsladder.falcon.utils.XssFilter
import jakarta.validation.Valid
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotEmpty
import jakarta.validation.constraints.Size

data class BindFileOrCreateFolderRequest(
    val s3FileId: String? = null,
    @field:Size(max = 255, message = "The maximum length is 255 letters")
    val name: String,
    @field:NotBlank(message = "currentFolderId cannot be null")
    val currentFolderId: String,
    val type: ClassroomFileType,
) {
    fun xssFilter(
        xssFilter: XssFilter,
    ) = BindFileOrCreateFolderRequest(
        s3FileId = s3FileId,
        name = xssFilter.filter(name),
        currentFolderId = currentFolderId,
        type = type,
    )

    fun toClassroomFile(
        classroomId: String,
        uploadedBy: String,
    ) = ClassroomFile(
        name = name,
        parentId = currentFolderId,
        state = ClassroomFileState.UNPUBLISHED,
        type = type,
        deleted = false,
        rootFolder = false,
        uploadedBy = uploadedBy,
        classroomId = classroomId,
    )
}

data class FileInfo(
    @field:NotBlank(message = "s3FileId cannot be null")
    val s3FileId: String,
    @field:Size(max = 255, message = "The maximum length is 255 letters")
    val name: String,
) {
    fun xssFilter(xssFilter: XssFilter) =
        FileInfo(
            s3FileId = s3FileId,
            name = xssFilter.filter(name),
        )

    fun toClassroomFile(
        classroomId: String,
        uploadedBy: String,
        currentFolderId: String,
    ) = ClassroomFile(
        name = name,
        parentId = currentFolderId,
        state = ClassroomFileState.UNPUBLISHED,
        type = ClassroomFileType.FILE,
        deleted = false,
        rootFolder = false,
        uploadedBy = uploadedBy,
        classroomId = classroomId,
    )
}

data class BindMultipleFilesRequest(
    @field:NotBlank(message = "currentFolderId cannot be null")
    val currentFolderId: String,
    @field:NotEmpty(message = "files cannot be empty")
    @field:Valid
    val files: List<FileInfo>,
) {
    fun xssFilter(xssFilter: XssFilter) =
        BindMultipleFilesRequest(
            currentFolderId = currentFolderId,
            files = files.map { it.xssFilter(xssFilter) },
        )
}
