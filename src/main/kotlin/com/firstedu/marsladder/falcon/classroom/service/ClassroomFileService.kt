package com.firstedu.marsladder.falcon.classroom.service

import com.firstedu.marsladder.falcon.classroom.ClassroomFileState
import com.firstedu.marsladder.falcon.classroom.service.domain.ClassroomFile
import com.firstedu.marsladder.falcon.classroom.service.domain.ClassroomFolderStructure
import com.firstedu.marsladder.falcon.file.service.domain.S3File
import org.springframework.web.multipart.MultipartFile

interface ClassroomFileService {
    fun getChildrenFilesByFolderId(
        classroomId: String,
        folderId: String,
    ): List<ClassroomFile>

    fun getParentFolderPathByFolderId(
        classroomId: String,
        folderId: String,
    ): List<ClassroomFile>

    fun getClassroomFileDetails(
        fileId: String,
    ): ClassroomFile

    fun initializeClassroomRootFolder(
        classroomId: String,
    ): ClassroomFile

    fun updateClassroomFileName(
        classroomId: String,
        fileId: String,
        name: String,
    )

    fun updateClassroomFileState(
        classroomId: String,
        fileId: String,
        state: ClassroomFileState,
    )

    fun deleteClassroomFile(
        classroomId: String,
        fileId: String,
    )

    fun creatClassroomFile(
        classroomFile: ClassroomFile,
        s3FileId: String?,
    ): ClassroomFile

    fun uploadClassroomFile(
        classroomId: String,
        file: MultipartFile,
    ): S3File

    fun getFolderStructure(
        classroomId: String,
        currentFileId: String?,
    ): ClassroomFolderStructure

    fun moveClassroomFile(
        classroomId: String,
        sourceFileId: String,
        targetFolderId: String,
    )
}
